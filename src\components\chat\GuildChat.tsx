import { useState, useEffect, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Send, Loader2, Users } from 'lucide-react';
import { toast } from 'sonner';
import { GuildMessageWithProfile, GuildMembership } from './types';

interface GuildChatProps {
  guildMembership: GuildMembership | null;
}

export function GuildChat({ guildMembership }: GuildChatProps) {
  const { user } = useAuth();
  const [messages, setMessages] = useState<GuildMessageWithProfile[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingMessages, setIsLoadingMessages] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom when messages change
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Fetch initial messages when guild changes
  useEffect(() => {
    if (guildMembership?.guild_id) {
      fetchMessages();
    } else {
      setMessages([]);
      setIsLoadingMessages(false);
    }
  }, [guildMembership?.guild_id]);

  // Set up real-time subscription
  useEffect(() => {
    if (!guildMembership?.guild_id) return;

    const channel = supabase
      .channel(`guild-messages-${guildMembership.guild_id}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'guild_messages',
          filter: `guild_id=eq.${guildMembership.guild_id}`,
        },
        async (payload) => {
          console.log('New guild message received:', payload);
          
          // Fetch the complete message with profile data
          const { data } = await supabase
            .from('guild_messages')
            .select('*, profiles(username, avatar_url)')
            .eq('id', payload.new.id)
            .single();

          if (data) {
            setMessages(prev => [...prev, data as GuildMessageWithProfile]);
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [guildMembership?.guild_id]);

  const fetchMessages = async () => {
    if (!guildMembership?.guild_id) return;

    try {
      setIsLoadingMessages(true);
      const { data, error } = await supabase
        .from('guild_messages')
        .select('*, profiles(username, avatar_url)')
        .eq('guild_id', guildMembership.guild_id)
        .order('created_at', { ascending: true })
        .limit(100);

      if (error) throw error;

      setMessages(data as GuildMessageWithProfile[]);
    } catch (error) {
      console.error('Error fetching guild messages:', error);
      toast.error('Failed to load guild messages');
    } finally {
      setIsLoadingMessages(false);
    }
  };

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newMessage.trim() || isLoading || !user?.id || !guildMembership?.guild_id) return;

    const messageContent = newMessage.trim();
    setNewMessage('');
    setIsLoading(true);

    try {
      const { error } = await supabase
        .from('guild_messages')
        .insert({
          content: messageContent,
          user_id: user.id,
          guild_id: guildMembership.guild_id,
        });

      if (error) throw error;

      console.log('Guild message sent successfully');
    } catch (error) {
      console.error('Error sending guild message:', error);
      toast.error('Failed to send message');
      setNewMessage(messageContent); // Restore message on error
    } finally {
      setIsLoading(false);
    }
  };

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  // Show "no guild" state
  if (!guildMembership) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <Users className="h-12 w-12 text-white/30 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-white mb-2">No Guild</h3>
          <p className="text-white/60">Join a guild to access guild chat</p>
        </div>
      </div>
    );
  }

  if (isLoadingMessages) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="flex items-center gap-2">
          <Loader2 className="h-5 w-5 animate-spin" />
          <span className="text-white/70">Loading guild messages...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-white/10">
        <h2 className="text-xl font-bold text-white">
          {guildMembership.guilds?.name || 'Guild Chat'}
        </h2>
        <p className="text-white/60 text-sm">Chat with your guild members</p>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <p className="text-white/50 text-center">
              No messages yet.<br />Start the guild conversation!
            </p>
          </div>
        ) : (
          messages.map((message) => {
            const isCurrentUser = message.user_id === user?.id;
            return (
              <div
                key={message.id}
                className={`flex items-start gap-3 ${isCurrentUser ? 'flex-row-reverse' : ''}`}
              >
                <Avatar className="h-8 w-8">
                  <AvatarImage src={message.profiles?.avatar_url || undefined} />
                  <AvatarFallback>
                    {message.profiles?.username?.[0]?.toUpperCase() || 'U'}
                  </AvatarFallback>
                </Avatar>
                <div className={`flex flex-col ${isCurrentUser ? 'items-end' : 'items-start'}`}>
                  <div className="flex items-center gap-2 mb-1">
                    <span className="text-white/70 text-sm font-medium">
                      {message.profiles?.username || 'Unknown User'}
                    </span>
                    <span className="text-white/40 text-xs">
                      {formatTime(message.created_at)}
                    </span>
                  </div>
                  <div
                    className={`p-3 rounded-lg max-w-xs md:max-w-md ${
                      isCurrentUser 
                        ? 'bg-electric/80 text-white' 
                        : 'bg-white/10 text-white'
                    }`}
                  >
                    <p className="text-sm break-words">{message.content}</p>
                  </div>
                </div>
              </div>
            );
          })
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <div className="p-4 border-t border-white/10">
        <form onSubmit={handleSendMessage} className="flex gap-2">
          <Input
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            placeholder="Type a message to guild chat..."
            disabled={isLoading}
            className="flex-1 bg-white/5 border-white/20 text-white placeholder:text-white/50"
            maxLength={500}
          />
          <Button 
            type="submit" 
            disabled={!newMessage.trim() || isLoading}
            className="bg-electric hover:bg-electric/80"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
          </Button>
        </form>
        {newMessage.length > 450 && (
          <p className="text-xs text-white/50 mt-1">
            {500 - newMessage.length} characters remaining
          </p>
        )}
      </div>
    </div>
  );
}
