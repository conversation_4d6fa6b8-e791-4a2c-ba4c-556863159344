import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Send } from 'lucide-react';
import { toast } from 'sonner';
import { sendGlobalMessage, sendGuildMessage } from './api';
import { ChatTab } from './types';

interface ChatInputProps {
  currentUserId: string;
  activeTab: ChatTab;
  guildId?: string;
  onOptimisticMessage: (content: string) => void;
}

export function ChatInput({ currentUserId, activeTab, guildId, onOptimisticMessage }: ChatInputProps) {
  const [newMessage, setNewMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newMessage.trim() || isLoading) return;

    const messageContent = newMessage.trim();
    setNewMessage('');
    setIsLoading(true);

    // Add optimistic message immediately
    onOptimisticMessage(messageContent);

    try {
      const startTime = Date.now();

      if (activeTab === 'global') {
        await sendGlobalMessage(messageContent, currentUserId);
      } else if (activeTab === 'guild' && guildId) {
        await sendGuildMessage(messageContent, currentUserId, guildId);
      }

      const endTime = Date.now();
      console.log(`Message sent successfully in ${endTime - startTime}ms`);

    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Failed to send message. Please try again.');
      // Note: In a production app, you'd want to remove the optimistic message here
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage(e as any);
    }
  };

  const isDisabled = activeTab === 'guild' && !guildId;

  return (
    <div className="p-4 border-t border-white/10 flex-shrink-0">
      <form onSubmit={handleSendMessage} className="flex gap-2">
        <Input
          value={newMessage}
          onChange={(e) => setNewMessage(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder={
            isDisabled 
              ? "Join a guild to chat here" 
              : activeTab === 'global' 
                ? "Type a message to global chat..." 
                : "Type a message to guild chat..."
          }
          disabled={isLoading || isDisabled}
          className="flex-1 bg-white/5 border-white/20 text-white placeholder:text-white/50"
          maxLength={500}
        />
        <Button 
          type="submit" 
          disabled={!newMessage.trim() || isLoading || isDisabled}
          className="bg-electric hover:bg-electric/80"
        >
          <Send className="h-4 w-4" />
        </Button>
      </form>
      {newMessage.length > 450 && (
        <p className="text-xs text-white/50 mt-1">
          {500 - newMessage.length} characters remaining
        </p>
      )}
    </div>
  );
}
