import { useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { MessageWithProfile, GuildMessageWithProfile } from '../types';

interface UseChatSubscriptionProps {
  activeTab: 'global' | 'guild';
  guildId?: string;
  onGlobalMessage: (message: MessageWithProfile) => void;
  onGuildMessage: (message: GuildMessageWithProfile) => void;
}

export function useChatSubscription({
  activeTab,
  guildId,
  onGlobalMessage,
  onGuildMessage
}: UseChatSubscriptionProps) {
  useEffect(() => {
    let globalSubscription: any = null;
    let guildSubscription: any = null;

    // Subscribe to global messages
    if (activeTab === 'global') {
      console.log('Setting up global messages subscription...');
      globalSubscription = supabase
        .channel('global-messages')
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'messages',
          },
          async (payload) => {
            console.log('Global message received:', payload.new);
            try {
              // Fetch the complete message with profile data
              const { data, error } = await supabase
                .from('messages')
                .select('*, profiles(username, avatar_url)')
                .eq('id', payload.new.id)
                .single();

              if (error) {
                console.error('Error fetching global message details:', error);
                return;
              }

              if (data) {
                console.log('Global message processed:', data);
                onGlobalMessage(data as MessageWithProfile);
              }
            } catch (error) {
              console.error('Error processing global message:', error);
            }
          }
        )
        .subscribe((status) => {
          console.log('Global subscription status:', status);
        });
    }

    // Subscribe to guild messages
    if (activeTab === 'guild' && guildId) {
      console.log('Setting up guild messages subscription for guild:', guildId);

      // Create a unique channel name to avoid conflicts
      const channelName = `guild-messages-${guildId}-${Date.now()}`;

      guildSubscription = supabase
        .channel(channelName)
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'guild_messages',
            filter: `guild_id=eq.${guildId}`,
          },
          async (payload) => {
            console.log('Guild message received via subscription:', payload.new);
            try {
              // Add a small delay to ensure the message is fully committed
              await new Promise(resolve => setTimeout(resolve, 100));

              // Fetch the complete message with profile data
              const { data, error } = await supabase
                .from('guild_messages')
                .select('*, profiles(username, avatar_url)')
                .eq('id', payload.new.id)
                .single();

              if (error) {
                console.error('Error fetching guild message details:', error);
                // Fallback: create message with basic data
                const fallbackMessage: GuildMessageWithProfile = {
                  ...payload.new,
                  profiles: null,
                  message_type: payload.new.message_type || 'user',
                  is_edited: payload.new.is_edited || false,
                } as GuildMessageWithProfile;
                onGuildMessage(fallbackMessage);
                return;
              }

              if (data) {
                console.log('Guild message processed successfully:', data);
                onGuildMessage(data as GuildMessageWithProfile);
              }
            } catch (error) {
              console.error('Error processing guild message:', error);
            }
          }
        )
        .subscribe((status) => {
          console.log('Guild subscription status:', status);
          if (status === 'SUBSCRIBED') {
            console.log('Guild subscription active for guild:', guildId);
          } else if (status === 'CHANNEL_ERROR') {
            console.error('Guild subscription error for guild:', guildId);
          }
        });
    }

    return () => {
      if (globalSubscription) {
        console.log('Cleaning up global subscription');
        supabase.removeChannel(globalSubscription);
      }
      if (guildSubscription) {
        console.log('Cleaning up guild subscription');
        supabase.removeChannel(guildSubscription);
      }
    };
  }, [activeTab, guildId, onGlobalMessage, onGuildMessage]);
}
