import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Globe, Shield } from 'lucide-react';
import { GlobalChat } from './GlobalChat';
import { GuildChat } from './GuildChat';
import { GuildMembership } from './types';

export function ChatPage() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<'global' | 'guild'>('global');

  // Fetch user's guild membership
  const { data: guildMembership, isLoading: guildLoading } = useQuery({
    queryKey: ['guild-membership', user?.id],
    queryFn: async () => {
      if (!user?.id) return null;

      const { data, error } = await supabase
        .from('guild_memberships')
        .select(`
          guild_id,
          guilds (
            name,
            icon_name
          )
        `)
        .eq('user_id', user.id)
        .eq('status', 'active')
        .single();

      if (error) {
        console.log('No guild membership found:', error.message);
        return null;
      }

      return data as GuildMembership;
    },
    enabled: !!user?.id,
  });

  if (!user) {
    return (
      <div className="flex items-center justify-center h-full">
        <p className="text-white/70">Please log in to access chat</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-gradient-to-br from-dark via-dark/95 to-electric/5">
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'global' | 'guild')} className="flex flex-col h-full">
        {/* Tab Navigation */}
        <div className="p-4 border-b border-white/10">
          <TabsList className="grid w-full grid-cols-2 bg-white/5">
            <TabsTrigger 
              value="global" 
              className="flex items-center gap-2 data-[state=active]:bg-electric/20 data-[state=active]:text-white"
            >
              <Globe className="h-4 w-4" />
              Global
            </TabsTrigger>
            <TabsTrigger 
              value="guild" 
              className="flex items-center gap-2 data-[state=active]:bg-electric/20 data-[state=active]:text-white"
            >
              <Shield className="h-4 w-4" />
              Guild
              {!guildMembership && !guildLoading && (
                <Badge variant="secondary" className="ml-1 text-xs">
                  None
                </Badge>
              )}
            </TabsTrigger>
          </TabsList>
        </div>

        {/* Tab Content */}
        <div className="flex-1 overflow-hidden">
          <TabsContent value="global" className="h-full m-0">
            <GlobalChat />
          </TabsContent>

          <TabsContent value="guild" className="h-full m-0">
            <GuildChat guildMembership={guildMembership} />
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
}
