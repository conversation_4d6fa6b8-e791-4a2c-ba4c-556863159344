import { useState, useCallback } from 'react';
import { MessageWithProfile, GuildMessageWithProfile, ChatTab } from '../types';

export function useChatMessages() {
  const [globalMessages, setGlobalMessages] = useState<MessageWithProfile[]>([]);
  const [guildMessages, setGuildMessages] = useState<GuildMessageWithProfile[]>([]);

  const addOptimisticMessage = useCallback((content: string, userId: string, activeTab: ChatTab, guildId?: string) => {
    const tempId = `temp-${Date.now()}`;
    const baseMessage = {
      id: tempId,
      content,
      user_id: userId,
      created_at: new Date().toISOString(),
      profiles: null, // Will be populated when real message arrives
    };

    console.log('Adding optimistic message:', { activeTab, content, tempId });

    if (activeTab === 'global') {
      const optimisticMessage: MessageWithProfile = baseMessage;
      setGlobalMessages(prev => {
        console.log('Adding optimistic global message, current count:', prev.length);
        return [...prev, optimisticMessage];
      });
    } else if (activeTab === 'guild' && guildId) {
      const optimisticMessage: GuildMessageWithProfile = {
        ...baseMessage,
        guild_id: guildId,
        message_type: 'user',
        is_edited: false,
      };
      setGuildMessages(prev => {
        console.log('Adding optimistic guild message, current count:', prev.length);
        return [...prev, optimisticMessage];
      });
    }
  }, []);

  const addGlobalMessage = useCallback((message: MessageWithProfile) => {
    setGlobalMessages(prev => {
      // Remove any optimistic message with similar content and timestamp
      const filtered = prev.filter(msg => 
        !msg.id.toString().startsWith('temp-') || 
        msg.content !== message.content ||
        Math.abs(new Date(msg.created_at).getTime() - new Date(message.created_at).getTime()) > 5000
      );
      return [...filtered, message];
    });
  }, []);

  const addGuildMessage = useCallback((message: GuildMessageWithProfile) => {
    console.log('Adding real guild message:', message);
    setGuildMessages(prev => {
      // Remove any optimistic message with similar content and timestamp
      const filtered = prev.filter(msg =>
        !msg.id.toString().startsWith('temp-') ||
        msg.content !== message.content ||
        Math.abs(new Date(msg.created_at).getTime() - new Date(message.created_at).getTime()) > 5000
      );
      console.log('Guild messages before:', prev.length, 'after filtering:', filtered.length, 'final count:', filtered.length + 1);
      return [...filtered, message];
    });
  }, []);

  return {
    globalMessages,
    guildMessages,
    setGlobalMessages,
    setGuildMessages,
    addOptimisticMessage,
    addGlobalMessage,
    addGuildMessage,
  };
}
